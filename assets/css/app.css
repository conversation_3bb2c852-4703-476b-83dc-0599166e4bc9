/* See the Tailwind configuration guide for advanced usage
   https://tailwindcss.com/docs/configuration */

@import "tailwindcss" source(none);
@source "../css";
@source "../js";
@source "../../lib/repobot_web";

/* A Tailwind plugin that makes "hero-#{ICON}" classes available.
   The heroicons installation itself is managed by your mix.exs */
@plugin "../vendor/heroicons";

/* daisyUI Tailwind Plugin. You can update this file by fetching the latest version with:
   curl -sLO https://github.com/saadeghi/daisyui/releases/latest/download/daisyui.js
   Make sure to look at the daisyUI changelog: https://daisyui.com/docs/changelog/ */
@plugin "../vendor/daisyui" {
  themes: false;
}

/* daisyUI theme plugin. You can update this file by fetching the latest version with:
  curl -sLO https://github.com/saadeghi/daisyui/releases/latest/download/daisyui-theme.js
  We ship with two themes, a light one inspired on Phoenix colors and a dark one inspired
  on Elixir colors. Build your own at: https://daisyui.com/theme-generator/ */
@plugin "../vendor/daisyui-theme" {
  name: "nord";
  default: true;
  prefersdark: false;
  color-scheme: "light";
  --color-base-100: oklch(95.127% 0.007 260.731);
  --color-base-200: oklch(93.299% 0.01 261.788);
  --color-base-300: oklch(89.925% 0.016 262.749);
  --color-base-content: oklch(32.437% 0.022 264.182);
  --color-primary: oklch(59.435% 0.077 254.027);
  --color-primary-content: oklch(11.887% 0.015 254.027);
  --color-secondary: oklch(69.651% 0.059 248.687);
  --color-secondary-content: oklch(13.93% 0.011 248.687);
  --color-accent: oklch(77.464% 0.062 217.469);
  --color-accent-content: oklch(15.492% 0.012 217.469);
  --color-neutral: oklch(45.229% 0.035 264.131);
  --color-neutral-content: oklch(89.925% 0.016 262.749);
  --color-info: oklch(69.207% 0.062 332.664);
  --color-info-content: oklch(13.841% 0.012 332.664);
  --color-success: oklch(76.827% 0.074 131.063);
  --color-success-content: oklch(15.365% 0.014 131.063);
  --color-warning: oklch(85.486% 0.089 84.093);
  --color-warning-content: oklch(17.097% 0.017 84.093);
  --color-error: oklch(60.61% 0.12 15.341);
  --color-error-content: oklch(12.122% 0.024 15.341);
  --radius-selector: 1rem;
  --radius-field: 0.25rem;
  --radius-box: 0.5rem;
  --size-selector: 0.25rem;
  --size-field: 0.25rem;
  --border: 1px;
  --depth: 0;
  --noise: 0;
}

/* Add variants based on LiveView classes */
@custom-variant phx-click-loading ([".phx-click-loading&", ".phx-click-loading &"]);
@custom-variant phx-submit-loading ([".phx-submit-loading&", ".phx-submit-loading &"]);
@custom-variant phx-change-loading ([".phx-change-loading&", ".phx-change-loading &"]);

/* Make LiveView wrapper divs transparent for layout */
[data-phx-root-id] {
  display: contents
}

/* This file is for your main application CSS */

@import "../node_modules/diff2html/bundles/css/diff2html.min.css";

/* This file is for your main application CSS */

.glow-border {
  position: relative;
  border-radius: 1rem;
  background: linear-gradient(135deg,
      rgba(30, 64, 175, 0.15) 0%,
      rgba(59, 130, 246, 0.1) 30%,
      rgba(245, 158, 11, 0.1) 70%,
      rgba(251, 146, 60, 0.15) 100%);
  padding: 2px;
  box-shadow:
    -35px 35px 65px -15px rgba(30, 64, 175, 0.25),
    35px -35px 65px -15px rgba(245, 158, 11, 0.25);
}

.glow-border::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 1rem;
  padding: 2px;
  background: linear-gradient(135deg,
      rgba(30, 64, 175, 0.3) 0%,
      rgba(59, 130, 246, 0.2) 30%,
      rgba(245, 158, 11, 0.2) 70%,
      rgba(251, 146, 60, 0.3) 100%);
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
}

@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.animate-spin-slow {
  animation: spin-slow 3s linear infinite;
}

/* Drag and drop styles */
.sortable-ghost {
  opacity: 0.5;
  background-color: #f0f9ff !important;
  /* Light blue background */
  border: 2px dashed #93c5fd !important;
  /* Blue dashed border */
}

.sortable-drag {
  opacity: 0.8;
  transform: scale(0.95);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.sortable-chosen {
  background-color: #f3f4f6 !important;
  /* Light gray background */
}

/* Custom styles for drag handle */
[data-drag-handle] {
  cursor: grab;
}

[data-drag-handle]:active {
  cursor: grabbing;
}
